import { DrawerMode } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSaveButton } from '@src/components/_molecules/IconButtons/SeaSaveButton'
import React, { useCallback } from 'react'
import { useDeviceWidth } from '@src/hooks/useDevice'

type PrimaryActionProps = {
  onSubmit?: () => void
  variant?: SeaButtonVariant
  title?: string
  // itemName?: string
}

interface DrawerPrimaryActionProps {
  mode: DrawerMode
  itemName?: string
  edit?: PrimaryActionProps
  create?: PrimaryActionProps
}

// TODO: Cater for additional drawer modes
export const DrawerPrimaryAction = ({ mode, itemName, edit, create }: DrawerPrimaryActionProps) => {
  const { isMobileWidth } = useDeviceWidth()

  const getTitle = useCallback(
    (actionType: PrimaryActionProps) => {
      let actionTitle = undefined
      if (mode === DrawerMode.Edit) {
        actionTitle = 'Update'
      }

      if (mode === DrawerMode.Create) {
        actionTitle = 'Add'
      }

      if (isMobileWidth) return actionTitle
      if (actionType.title) return actionType.title
      if (itemName) return `${actionTitle} ${itemName}`
      // if (actionType.itemName) return `${actionTitle} ${actionType.itemName}`
      return actionTitle
    },
    [mode]
  )

  if (mode === DrawerMode.Edit && edit) {
    return (
      <SeaEditButton
        variant={edit?.variant ?? SeaButtonVariant.Primary}
        onPress={edit.onSubmit}
        label={getTitle(edit)}
      />
    )
  }

  if (mode === DrawerMode.Create && create) {
    return (
      <SeaSaveButton
        variant={create?.variant ?? SeaButtonVariant.Primary}
        onPress={create.onSubmit}
        label={getTitle(create)}
      />
    )
  }

  return null
}

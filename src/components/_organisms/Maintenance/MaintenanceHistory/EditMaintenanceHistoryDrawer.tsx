import React, { use<PERSON>allback, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { MaintenanceTaskCompleted } from '@src/shared-state/VesselMaintenance/maintenanceTasksCompleted'
import { sharedState } from '@src/shared-state/shared-state'
import { EditSeaEquipment } from '../../SeaEquipment/EditSeaEquipment'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { addInterval, formatSeaDatetime, makeDateTime, toMillis } from '@src/lib/datesAndTime'
import { FormikValues, useFormik } from 'formik'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { cleanupStringArray, preventMultiTap } from '@src/lib/util'
import { SeaTagsInput } from '@src/components/_atoms/_inputs/SeaTagsInput/SeaTagsInput'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import {
  JobDto,
  MaintenanceTaskDto,
  UpdateMaintenanceHistoryDto,
  UpdateMaintenanceHistoryUseCase,
} from '@src/domain/use-cases/maintenance/UpdateMaintenanceHistoryUseCase'
import { renderCategoryName } from '@src/lib/categories'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateMaintenanceHistoryUseCase } from '@src/domain/use-cases/maintenance/CreateMaintenanceHistoryUseCase'
import Yup, { notTooOld } from '@src/lib/yup'
import { SeaFile } from '@src/lib/fileImports'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const validationSchema = Yup.object({
  task: Yup.string().max(5000).required(),
  description: Yup.string().max(5000),
  whenCompleted: Yup.date()
    .required()
    .min(...notTooOld),
})
export interface EditMaintenanceHistoryDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: MaintenanceTaskCompleted
  mode?: DrawerMode
}

export function EditMaintenanceHistoryDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditMaintenanceHistoryDrawerProps) {
  const userId = sharedState.userId.use(visible ? 1 : 500)

  const licenseeId = sharedState.licenseeId.use(visible ? 1 : 500)
  const vessel = sharedState.vessel.use(visible ? 1 : 500)
  const vesselId = sharedState.vesselId.use(visible ? 1 : 500)
  const vesselLocations = sharedState.vesselLocations.use(visible ? 1 : 500)
  const vesselEquipment = sharedState.equipment.use(visible ? 1 : 500)
  const vesselSystems = sharedState.vesselSystems.use(visible ? 1 : 500)
  const spareParts = sharedState.spareParts.use(visible ? 1 : 500)
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use(visible ? 1 : 500)
  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use(visible ? 1 : 500)

  // Hooks
  const [equipmentData, setEquipmentData] = useState<Partial<Equipment>>({})
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      task: selectedItem?.task ?? '',
      description: selectedItem?.description ?? undefined,
      maintenanceTags: selectedItem?.maintenanceTags,
      whenCompleted: selectedItem?.whenCompleted ? formatSeaDatetime(selectedItem.whenCompleted) : '',
    }
  }, [selectedItem])

  const handleSubmit = useCallback(
    async (data: FormikValues) => {
      if (preventMultiTap('maintenanceHistory')) {
        return
      }

      if (!vesselId || !userId || !licenseeId) {
        console.error('Vessel ID is not available')
        return
      }

      let maintenanceTaskDto: MaintenanceTaskDto | undefined = undefined
      let executeMaintenanceTask = false
      if (selectedItem?.maintenanceTaskId) {
        const scheduledTask = scheduledMaintenanceTasks?.byId?.[selectedItem?.maintenanceTaskId]
        let whenLastService = data.whenCompleted ? (toMillis(data.whenCompleted) as number) : 0
        const completedMaintenanceTasks =
          maintenanceTasksCompleted?.byMaintenanceTaskId?.[selectedItem?.maintenanceTaskId]
        let engineHoursLastService = selectedItem?.engineHours
        const useWeekMonth =
          scheduledTask?.intervalType === 'weekMonth' || scheduledTask?.intervalType === 'weekMonthAndHours'
        const useEngineHours =
          scheduledTask?.intervalType === 'engineHours' || scheduledTask?.intervalType === 'weekMonthAndHours'

        if (completedMaintenanceTasks?.length) {
          // Find the most recent completed maintenance task
          for (const task of completedMaintenanceTasks) {
            // Ignore tasks without a whenCompleted date or the current task
            if (!task.whenCompleted || task.id === selectedItem?.id) continue
            // Update the latest whenCompleted and engine hours if applicable
            if (task.whenCompleted > whenLastService) {
              whenLastService = task.whenCompleted
              engineHoursLastService = task.engineHours // Assuming each task has engine hours recorded
            }
          }
        }

        maintenanceTaskDto = {
          id: selectedItem?.maintenanceTaskId,
          whenLastService: whenLastService ? whenLastService : undefined,
          dateCompleted: useWeekMonth
            ? (addInterval(whenLastService, scheduledTask?.intervalWeekMonth).toISODate() ?? undefined)
            : undefined,
          engineHoursDue: useEngineHours
            ? Number(engineHoursLastService) + (scheduledTask?.intervalEngineHours ?? 0)
            : 0,
        }

        executeMaintenanceTask = !!(scheduledTask?.id && scheduledTask?.intervalWeekMonth)
      }

      let jobDto: JobDto | undefined = undefined

      if (selectedItem?.type === 'job') {
        const equipment = equipmentData.id ? vesselEquipment?.byId?.[equipmentData.id] : undefined

        const systemName =
          vesselSystems && equipment?.systemId ? vesselSystems.byId[equipment.systemId]?.name : undefined

        jobDto = {
          id: selectedItem?.id,
          task: data.task,
          jobNum: selectedItem?.jobNum,
          description: data.description,
          system: systemName,
          equipment: equipment?.equipment,
          location: equipmentData.locationId
            ? renderCategoryName(equipmentData.locationId, vesselLocations)
            : undefined,
          isCritical: selectedItem?.isCritical,
          completedByName: renderFullNameForUserId(selectedItem?.completedBy),
          whenCompleted: data.whenCompleted,
          notes: selectedItem?.notes,
          maintenanceTags: data.maintenanceTags,
          actualTime: selectedItem?.actualTime,
          //TODO: parts Used
          // partsUSed: renderSparePartsUsedAsString(sparePartsStock, spareParts),
        }
      }

      const dto: UpdateMaintenanceHistoryDto = {
        vesselId: vesselId,
        itemId: selectedItem?.id ?? '',
        task: data.task,
        description: data.description,
        maintenanceTags: cleanupStringArray(data.maintenanceTags) ?? [],
        whenCompleted: data.whenCompleted ? toMillis(data.whenCompleted) : Date.now(),
        equipmentId: equipmentData.id ?? '',
        //TODO: Location ID logic to update the data in the firebase collection
        locationId: equipmentData.locationId ?? '',
        spareParts: selectedItem?.spareParts ?? {},
        files: files ?? [],
        ...(mode === DrawerMode.Edit
          ? {
              maintenanceTask: maintenanceTaskDto ?? undefined,
              job: jobDto ?? undefined,
            }
          : {}),
      }

      if (mode === DrawerMode.Create) {
        const createMaintenanceHistory = services.get(CreateMaintenanceHistoryUseCase)

        createMaintenanceHistory
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Maintenance History\n ${err.message}`))
      } else {
        const updateMaintenanceHistroy = services.get(UpdateMaintenanceHistoryUseCase)

        updateMaintenanceHistroy
          .execute(dto, userId, licenseeId, executeMaintenanceTask)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
      }
    },
    [
      mode,
      vesselId,
      userId,
      licenseeId,
      selectedItem,
      equipmentData,
      scheduledMaintenanceTasks,
      maintenanceTasksCompleted,
      files,
    ]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  return (
    <SeaDrawer
      title={`${mode === DrawerMode.Create ? 'Add' : 'Edit'} Completed Maintenance Task`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Maintenance Task'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <EditSeaEquipment
            equipmentId={selectedItem?.equipment?.id}
            locationId={selectedItem?.equipment?.locationId}
            mode={'view'}
            onChange={equipment => setEquipmentData(equipment)}
          />
        </DrawerRow>
        <DrawerRow>
          <SeaDateTimeInput
            value={makeDateTime(formik.values.whenCompleted)}
            onChange={date => formik.setFieldValue('whenCompleted', date)}
            type={'date'}
            label={'When Completed'}
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
            hasError={Boolean(errors.whenCompleted && touched.whenCompleted)}
            errorText={errors.whenCompleted}
          />
          <DrawerColumn>
            <SeaTagsInput
              label={'Maintenance Tags'}
              showIcon={true}
              tags={formik.values.maintenanceTags ?? []}
              setTags={tags => formik.setFieldValue('maintenanceTags', tags)}
              options={vessel?.possibleMaintenanceTags}
            />
          </DrawerColumn>
        </DrawerRow>
        <DrawerRow>
          <SeaTextInput
            label={'Maintenance Task'}
            showIcon={true}
            multiLine={true}
            value={formik.values.task}
            onChangeText={formik.handleChange('task')}
            hasError={Boolean(errors.task && touched.task)}
            errorText={errors.task}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaTextInput
            label={'Description'}
            showIcon={true}
            multiLine={true}
            value={formik.values.description}
            onChangeText={formik.handleChange('description')}
            hasError={Boolean(errors.description && touched.description)}
            errorText={errors.description}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaFileUploader initialFiles={selectedItem?.files ?? []} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}

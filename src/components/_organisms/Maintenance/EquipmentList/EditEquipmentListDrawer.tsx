import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { renderCategoryName } from '@src/lib/categories'
import { FormikValues, useFormik } from 'formik'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { UpdateEquipmentDto, UpdateEquipmentUseCase } from '@src/domain/use-cases/maintenance/UpdateEquipmentUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateEquipmentDto, CreateEquipmentUseCase } from '@src/domain/use-cases/maintenance/CreateEquipmentUseCase'

import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

const validationSchema = Yup.object({
  equipment: Yup.string().max(500).required(),
  make: Yup.string().max(200),
  model: Yup.string().max(200),
  serialNumber: Yup.string().max(200),
  systemId: Yup.string().required(),
  locationId: Yup.string(),
  isCritical: Yup.boolean(),
  contactIds: Yup.array().of(Yup.string()),
  equipmentDocumentIds: Yup.array().of(Yup.string()),
})

export interface EditEquipmentListDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  equipmentId?: string
  mode: DrawerMode
}

export function EditEquipmentListDrawer({ equipmentId, visible, onClose, style, mode }: EditEquipmentListDrawerProps) {
  const vessel = sharedState.vessel.use(visible)
  const equipment = sharedState.equipment.use(visible)
  const vesselSystems = sharedState.vesselSystems.use(visible)
  const vesselLocations = sharedState.vesselLocations.use(visible)
  const contacts = sharedState.contacts.use(visible)
  const equipmentManualDocuments = sharedState.equipmentManualDocuments.use(visible)
  const userId = sharedState.userId.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const selectedEquipment = useMemo(() => {
    if (!equipmentId) return undefined
    return equipment?.byId[equipmentId]
  }, [equipment, equipmentId])

  const initialValues = useMemo(() => {
    return {
      id: selectedEquipment?.id ?? '',
      systemId: selectedEquipment?.systemId ?? '',
      equipment: selectedEquipment?.equipment ?? '',
      locationId: selectedEquipment?.locationId ?? '',
      make: selectedEquipment?.make ?? '',
      model: selectedEquipment?.model ?? '',
      serial: selectedEquipment?.serial ?? '',
      notes: selectedEquipment?.notes ?? '',
      isCritical: selectedEquipment?.isCritical ? true : false,
      contactIds: selectedEquipment?.contactIds ?? [],
      equipmentDocumentIds: selectedEquipment?.equipmentDocumentIds ?? [],

      vesselId: vessel?.id,
    }
  }, [selectedEquipment, vessel])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId) {
        console.error('Vessel ID, Licensee ID, or User ID is not available')
        return
      }

      const commonDto = {
        vesselId,

        systemId: values.systemId,
        equipment: values.equipment.trim(),
        locationId: values.locationId,
        make: values.make ?? undefined,
        model: values.model ?? undefined,
        serial: values.serial ?? undefined,
        notes: values.notes ?? undefined,
        isCritical: values.isCritical ? true : false,
        files: files,
        equipmentDocumentIds: values.equipmentDocumentIds ?? [],
        contactIds: values.contactIds ?? [],
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateEquipmentDto = {
          ...commonDto,
        }
        const createSparePart = services.get(CreateEquipmentUseCase)

        createSparePart.execute(dto, userId, licenseeId).then(() => {
          onClose()
        })
      } else {
        const dto: UpdateEquipmentDto = {
          ...commonDto,
          id: selectedEquipment?.id ?? '',
        }

        const updateSparePart = services.get(UpdateEquipmentUseCase)

        updateSparePart.execute(dto, userId, licenseeId).then(() => {
          onClose()
        })
      }
    },
    [vesselId, licenseeId, userId, selectedEquipment, mode, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  const categoryOptions = useMemo(() => {
    if (!vesselSystems?.ids) return []

    const options = vesselSystems.ids
      .filter(id => vesselSystems.byId[id].state === 'active')
      .map(id => ({
        label: renderCategoryName(id, vesselSystems),
        value: id,
      }))

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...options,
    ]
  }, [vesselSystems])

  const locationOptions = useMemo(() => {
    if (!vesselLocations?.ids) return []

    const options = vesselLocations.ids
      .filter(id => vesselLocations.byId[id].state === 'active')
      .map(id => ({
        label: renderCategoryName(id, vesselLocations),
        value: id,
      }))

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...options,
    ]
  }, [vesselLocations])

  const contactOptions = useMemo(() => {
    if (!contacts?.all) return []
    return contacts?.all?.map(contact => {
      return {
        value: contact.id,
        label: contact.company ? `${contact.company}, ${contact.name}` : contact.name,
      }
    })
  }, [contacts])

  const equipmentManualDocumentOptions = useMemo(() => {
    if (!equipmentManualDocuments?.documents) return []
    return equipmentManualDocuments?.documents?.map(document => {
      return {
        value: document.id,
        label: document.title,
      }
    })
  }, [equipmentManualDocuments])

  const onEquipmentManualSelect = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = [...formik.values.equipmentDocumentIds]

          newIds.push(changedValue)

          formik.setFieldValue('equipmentDocumentIds', newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = formik.values.equipmentDocumentIds.filter(id => id !== changedValue)

          formik.setFieldValue('equipmentDocumentIds', newIds)

          return
        }
        default:
          return
      }
    },
    [formik]
  )

  const onContactSelect = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = [...formik.values.contactIds]

          newIds.push(changedValue)

          formik.setFieldValue('contactIds', newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = formik.values.contactIds.filter(id => id !== changedValue)

          formik.setFieldValue('contactIds', newIds)

          return
        }
        default:
          return
      }
    },
    [formik]
  )

  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Equipment' : 'Edit Equipment'}
      visible={visible}
      onClose={onClose}
      style={style}
      level={2}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Equipment'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaDropdown
            label={'System'}
            labelIconOptions={{
              icon: 'dns',
              size: 18,
            }}
            showIcon={true}
            items={categoryOptions}
            onSelect={value => formik.setFieldValue('systemId', value)}
            value={formik.values.systemId}
            style={{ flex: 1, width: '100%' }}
            hasError={Boolean(errors.systemId && touched.systemId)}
            errorText={errors.systemId}
          />
          <SeaTextInput
            label={'Equipment'}
            showIcon={true}
            value={formik.values.equipment}
            onChangeText={formik.handleChange('equipment')}
            style={{ flex: 1 }}
            hasError={Boolean(errors.equipment && touched.equipment)}
            errorText={errors.equipment}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaDropdown
            label={'Location'}
            labelIconOptions={{ icon: 'location_on' }}
            showIcon={true}
            items={locationOptions}
            onSelect={value => formik.setFieldValue('locationId', value)}
            value={formik.values.locationId}
            style={{ flex: 1, width: '100%' }}
            hasError={Boolean(errors.locationId && touched.locationId)}
            errorText={errors.locationId}
          />

          <SeaCheckbox
            heading={'Critical Equipment'}
            label={'This equipment is critical'}
            showIcon={true}
            value={formik.values.isCritical}
            onChange={value => formik.setFieldValue('isCritical', value ?? false)}
            hasError={Boolean(errors.isCritical && touched.isCritical)}
            style={{ flex: 1, width: '100%' }}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaTextInput
            label={'Make'}
            showIcon={true}
            value={formik.values.make}
            onChangeText={formik.handleChange('make')}
            style={{ flex: 1 }}
            hasError={Boolean(errors.make && touched.make)}
            errorText={errors.make}
          />

          <SeaTextInput
            label={'Model'}
            showIcon={true}
            value={formik.values.model}
            onChangeText={formik.handleChange('model')}
            style={{ flex: 1 }}
            hasError={Boolean(errors.model && touched.model)}
            errorText={errors.model}
          />

          <SeaTextInput
            label={'Serial'}
            showIcon={true}
            value={formik.values.serial}
            onChangeText={formik.handleChange('serial')}
            style={{ flex: 1 }}
            hasError={Boolean(errors.serial && touched.serial)}
            errorText={errors.serial}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaTextInput
            label={'Equipment Notes'}
            showIcon={true}
            multiLine
            value={formik.values.notes}
            onChangeText={formik.handleChange('notes')}
            style={{ flex: 1 }}
            hasError={Boolean(errors.notes && touched.notes)}
            errorText={errors.notes}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaFileUploader initialFiles={selectedEquipment?.files} files={files} setFiles={setFiles} />
        </DrawerRow>

        {!vessel?.isShoreFacility && (equipmentManualDocumentOptions?.length || 0) > 0 && (
          <DrawerRow>
            <SeaSelectInput
              label="Connect Equipment Manual"
              showIcon={true}
              isMulti={true}
              showSelectAllOption={false}
              data={equipmentManualDocumentOptions}
              selectedItemValues={formik.values.equipmentDocumentIds}
              style={{ width: '100%' }}
              onItemSelect={onEquipmentManualSelect}
            />
          </DrawerRow>
        )}

        <DrawerRow>
          <SeaSelectInput
            label={'Connect Contact'}
            showIcon={true}
            isMulti={true}
            showSelectAllOption={false}
            data={contactOptions}
            selectedItemValues={formik.values.contactIds}
            style={{ width: '100%' }}
            onItemSelect={onContactSelect}
          />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}

import React, { useMemo, useState } from 'react'
import {
  authoritiesNotified,
  Incident,
  IncidentState,
  IncidentType,
  IncidentWhoInvolvedTypes,
} from '@src/shared-state/HealthSafety/incidents'
import { StyleSheet } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { useFormik } from 'formik'
import { formatValue, preventMultiTap } from '@src/lib/util'
import { formatSeaDatetime, makeDateTime, toMillis } from '@src/lib/datesAndTime'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { IncidentCategorySelectInput } from '@src/components/_molecules/IncidentCategorySelectInput/IncidentCategorySelectInput'
import { PersonnelInvolvedTypesSelectInput } from '@src/components/_molecules/PersonnelInvolvedTypesSelectInput/PersonnelInvolvedTypesSelectInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { IncidentCauseSelectInput } from '@src/components/_molecules/IncidentCauseSelectInput/IncidentCauseSelectInput'
import { SeaSignatureImage } from '@src/components/_atoms/SeaSignatureImage/SeaSignatureImage'
import { IncidentStatus } from '@src/components/_molecules/IncidentStatus/IncidentStatus'
import Yup, { notTooOld } from '@src/lib/yup'
import {
  CreateIncidentDto,
  CreateIncidentUseCase,
  IncidentDto,
} from '@src/domain/use-cases/incident/CreateIncidentUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { UpdateIncidentDto, UpdateIncidentUseCase } from '@src/domain/use-cases/incident/UpdateIncidentUseCase'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'

import { SeaFile } from '@src/lib/fileImports'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

interface EditIncidentProps {
  incident?: Incident
  visible?: boolean
  onClose?: () => void
  mode?: DrawerMode
}

const validationSchema = Yup.object({
  vesselId: Yup.string().required(),
  type: Yup.string().max(200).required(),
  name: Yup.string().max(500).required(),
  categoryId: Yup.string().max(200),
  isSensitive: Yup.boolean(),
  reportedBy: Yup.string().max(500),
  role: Yup.string().max(500),
  whoInvolved: Yup.string().max(500),
  witnesses: Yup.string().max(5000),
  whenEvent: Yup.date()
    .max(formatSeaDatetime())
    .required()
    .min(...notTooOld),
  location: Yup.string().max(500),
  conditions: Yup.string().max(5000),
  notifiedAuthorities: Yup.string().max(200),
  authority: Yup.string().max(200),
  propertyDamage: Yup.string().max(5000),
  description: Yup.string().max(5000),
  initialActions: Yup.string().max(5000),
  prevention: Yup.string().max(5000),
})

export const EditIncident = ({
  incident,
  visible = false,
  onClose = () => {},
  mode = DrawerMode.Edit,
}: EditIncidentProps) => {
  if (!incident) {
    return <></>
  }
  const isNew = mode === DrawerMode.Create

  // Shared Data
  const user = sharedState.user.use()
  const vesselId = sharedState.vesselId.use()
  const licenseeId = sharedState.licenseeId.use()

  // Hooks
  const [vesselIds, setVesselIds] = useState<string[]>([incident.vesselId ?? vesselId])
  const [categoryId, setCategoryId] = useState<string>(incident.categoryId ?? '')
  const [whoInvolvedTypes, setWhoInvolvedTypes] = useState<[keyof IncidentWhoInvolvedTypes]>(
    incident.whoInvolvedTypes ?? []
  )
  const [causeIds, setCauseIds] = useState<string[]>(incident.causeIds ?? [])
  const [files, setFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  const eventTypeItems = Object.entries(IncidentType).map(([key, value]) => ({
    label: value,
    value: key,
  }))

  const authoritiesNotifiedOptions = Object.entries(authoritiesNotified).map(([key, value]) => ({
    label: value,
    value: key,
  }))

  const initialValues = useMemo(() => {
    return {
      vesselId: vesselIds?.[0] ?? vesselId,
      type: incident.type ?? '',
      name: incident.name ?? '',
      categoryId: incident.categoryId ?? '',
      isSensitive: incident.isSensitive === 'y',
      reportedBy: incident.reportedBy ?? '',
      role: incident.role ?? '',
      whoInvolved: incident.whoInvolved ?? '',
      witnesses: incident.witnesses ?? '',
      whenEvent: incident.whenEvent ? formatSeaDatetime(incident.whenEvent) : '',
      location: incident.location ?? '',
      conditions: incident.conditions ?? '',
      notifiedAuthorities: incident.notifiedAuthorities === 'y' ? 'y' : 'n',
      authority: incident.authority ?? '',
      propertyDamage: incident.propertyDamage ?? '',
      description: incident.description ?? '',
      initialActions: incident.initialActions ?? '',
      prevention: incident.prevention ?? '',
    }
  }, [incident])

  const onSubmit = (values: typeof initialValues) => {
    if (preventMultiTap('incident')) {
      return
    }

    // TODO: Cater for:
    //  - signature

    const incidentDto: IncidentDto = {
      // TODO: Make a new category ID
      categoryId: values.categoryId,
      causeIds: causeIds,
      // TODO: Set Completed
      // completedBy: undefined,
      conditions: values.conditions,
      description: values.description,
      files: files,
      initialActions: values.initialActions,
      injuries: [],
      location: values.location,
      name: values.name,
      notifiedAuthorities: values.notifiedAuthorities,
      prevention: values.prevention,
      propertyDamage: values.propertyDamage,
      // reportNum: values.reportNum,
      reportedBy: values.reportedBy,
      role: values.role,
      // TODO: Send Signature
      signature: '',
      type: values.type as string,
      whenEvent: toMillis(values.whenEvent),
      whoInvolved: values.whoInvolved,
      whoInvolvedTypes: whoInvolvedTypes as string[],
      witnesses: values.witnesses,
    }

    if (isNew) {
      /** Create New Incident */
      const createIncidentDto: CreateIncidentDto = {
        ...incidentDto,
        state: IncidentState.draft,
        vesselId: vesselIds?.[0] ?? vesselId,
      }

      const createIncidentUseCase = services.get(CreateIncidentUseCase)
      createIncidentUseCase
        .execute(createIncidentDto, user?.id, licenseeId)
        .then(() => {
          alert('Incident created successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    } else {
      /** Update/Edit Incident */

      const updateIncidentDto: UpdateIncidentDto = {
        // TODO: check here
        ...incidentDto,
        id: incident.id,
        state: incident.state,
      }

      const updateIncidentUseCase = services.get(UpdateIncidentUseCase)
      updateIncidentUseCase
        .execute(updateIncidentDto, user?.id, licenseeId, vesselIds?.[0] ?? vesselId)
        .then(() => {
          alert('Incident Updated successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    }
  }

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => onSubmit(values),
  })

  const { errors, touched } = formik

  const title = `${isNew ? 'Create Report' : 'Edit Report - '}${incident.name}`
  return (
    <SeaDrawer
      title={title}
      titleAdditionalElements={() => incident?.state && <IncidentStatus status={incident.state} />}
      visible={visible}
      onClose={() => onClose()}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Report'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        {/** Card 1 */}
        <>
          {/** Card 1 - Row 3 */}
          {!isNew && incident.reportNum && (
            <SeaStack direction={'column'} align={'end'} isCollapsible={true} width={'100%'}>
              <SeaTypography variant={'title'}>Report #{formatValue(incident?.reportNum)}</SeaTypography>
            </SeaStack>
          )}

          <DrawerRow>
            <SeaTypography variant={'subtitle'}>Type</SeaTypography>
          </DrawerRow>

          {/** Card 1 - Row 1 */}
          <DrawerRow>
            <VesselSelectInput showIcon={true} vesselIds={vesselIds} setVesselIds={setVesselIds} isMulti={false} />

            <SeaDropdown
              label={'Type of Event'}
              showIcon={true}
              items={eventTypeItems}
              value={incident?.type ? String(incident.type) : undefined}
              onSelect={value => formik.setFieldValue('type', value)}
              style={styles.column}
              hasError={Boolean(errors.type && touched.type)}
              errorText={errors.type}
            />
          </DrawerRow>

          {/** Card 1 - Row 2 */}
          <DrawerRow>
            <SeaTextInput
              label={'Title of Event'}
              showIcon={true}
              value={formik.values.name}
              onChangeText={formik.handleChange('name')}
              style={styles.column}
              hasError={Boolean(errors.name && touched.name)}
              errorText={errors.name}
            />

            <IncidentCategorySelectInput
              showIcon={true}
              visible={true}
              categoryId={categoryId}
              setCategoryId={setCategoryId}
            />
          </DrawerRow>
        </>

        {/** Card 2 */}
        <>
          <DrawerRow>
            <SeaTypography variant={'subtitle'}>Personnel</SeaTypography>
          </DrawerRow>

          {/** Card 2 - Row 2 */}
          <DrawerRow>
            <PersonnelInvolvedTypesSelectInput
              showIcon={true}
              items={whoInvolvedTypes as string[]}
              setItems={setWhoInvolvedTypes as React.Dispatch<React.SetStateAction<string[]>>}
            />

            <SeaTextInput
              label={'First Reported By'}
              showIcon={true}
              labelIconOptions={{ icon: 'person' }}
              value={formik.values.reportedBy}
              onChangeText={formik.handleChange('reportedBy')}
              style={styles.column}
              hasError={Boolean(errors.reportedBy && touched.reportedBy)}
              errorText={errors.reportedBy}
            />
          </DrawerRow>

          {/** Card 2 - Row 2 */}
          <DrawerRow>
            <SeaTextInput
              label={'What was their role in the event?'}
              showIcon={true}
              value={formik.values.role}
              onChangeText={formik.handleChange('role')}
              style={styles.column}
              hasError={Boolean(errors.role && touched.role)}
              errorText={errors.role}
            />

            <SeaTextInput
              label={'Names of those involved'}
              showIcon={true}
              labelIconOptions={{ icon: 'person' }}
              value={formik.values.whoInvolved}
              onChangeText={formik.handleChange('whoInvolved')}
              style={styles.column}
              hasError={Boolean(errors.whoInvolved && touched.whoInvolved)}
              errorText={errors.whoInvolved}
            />
          </DrawerRow>

          {/** Card 2 - Row 3 */}
          <DrawerRow>
            <SeaTextInput
              label={'Witnesses'}
              showIcon={true}
              labelIconOptions={{ icon: 'person' }}
              value={formik.values.witnesses}
              onChangeText={formik.handleChange('witnesses')}
              style={styles.column}
              hasError={Boolean(errors.witnesses && touched.witnesses)}
              errorText={errors.witnesses}
            />
          </DrawerRow>
        </>

        {/** Card 3 */}
        <>
          <DrawerRow>
            <SeaTypography variant={'subtitle'}>Event</SeaTypography>
          </DrawerRow>

          {/** Card 3 - Row 1 */}
          <DrawerRow>
            <SeaDateTimeInput
              label={'Time of Event'}
              showIcon={true}
              value={makeDateTime(formik.values.whenEvent)}
              // onChange={formik.handleChange("whenEvent")}
              onChange={() => alert('TODO: DateTime')}
              type={'datetime'}
              style={styles.column}
              hasError={Boolean(errors.whenEvent && touched.whenEvent)}
              errorText={errors.whenEvent}
            />

            <SeaTextInput
              label={'Event Location'}
              showIcon={true}
              labelIconOptions={{ icon: 'location_on' }}
              value={formik.values.location}
              onChangeText={formik.handleChange('location')}
              style={styles.column}
              hasError={Boolean(errors.location && touched.location)}
              errorText={errors.location}
            />
          </DrawerRow>

          {/** Card 3 - Row 2 */}
          <DrawerRow>
            <SeaTextInput
              label={'Environmental Conditions (If Applicable)'}
              showIcon={true}
              value={formik.values.conditions}
              onChangeText={formik.handleChange('conditions')}
              style={styles.column}
              hasError={Boolean(errors.conditions && touched.conditions)}
              errorText={errors.conditions}
            />

            <SeaDropdown
              label={'Have authorities been notified? (ICON)'}
              showIcon={true}
              value={formik.values.notifiedAuthorities}
              items={authoritiesNotifiedOptions}
              onSelect={value => formik.setFieldValue('notifiedAuthorities', value)}
              style={styles.column}
              hasError={Boolean(errors.notifiedAuthorities && touched.notifiedAuthorities)}
              errorText={errors.notifiedAuthorities}
            />
          </DrawerRow>

          {/** Card 3 - Row 3 */}
          <DrawerRow>
            <SeaStack style={styles.column}>
              <IncidentCauseSelectInput showIcon={true} causeIds={causeIds} setCauseIds={setCauseIds} />
            </SeaStack>

            <SeaTextInput
              label={'Is there any damage to property?'}
              showIcon={true}
              value={formik.values.propertyDamage}
              onChangeText={formik.handleChange('propertyDamage')}
              style={styles.column}
              hasError={Boolean(errors.propertyDamage && touched.propertyDamage)}
              errorText={errors.propertyDamage}
            />
          </DrawerRow>

          {/** Card 3 - Row 4 */}
          <DrawerRow>
            <SeaTextInput
              label={'What Happened?'}
              showIcon={true}
              value={formik.values.description}
              onChangeText={formik.handleChange('description')}
              multiLine={true}
              style={styles.column}
              hasError={Boolean(errors.description && touched.description)}
              errorText={errors.description}
            />
          </DrawerRow>

          {/** Card 3 - Row 5 */}
          <DrawerRow>
            <SeaTextInput
              label={'What Initial actions have you taken?'}
              showIcon={true}
              value={formik.values.initialActions}
              onChangeText={formik.handleChange('initialActions')}
              multiLine={true}
              style={styles.column}
              hasError={Boolean(errors.initialActions && touched.initialActions)}
              errorText={errors.initialActions}
            />

            <SeaTextInput
              label={'Do you suggest any corrective actions?'}
              showIcon={true}
              value={formik.values.prevention}
              onChangeText={formik.handleChange('prevention')}
              multiLine={true}
              style={styles.column}
              hasError={Boolean(errors.prevention && touched.prevention)}
              errorText={errors.prevention}
            />
          </DrawerRow>

          {/** Card 3 - Row 6 */}
          <DrawerRow>
            <SeaFileUploader initialFiles={incident.files} files={files} setFiles={setFiles} />
          </DrawerRow>
        </>

        {/** Card 4 */}

        <>
          <SeaSignatureImage />
        </>
      </DrawerContent>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  column: {
    flex: 1,
    width: '100%',
  },
})

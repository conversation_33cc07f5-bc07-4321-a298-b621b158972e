import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'

import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'

import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { FormikValues, useFormik } from 'formik'
import { formatSeaDate, subtractInterval } from '@src/lib/datesAndTime'
import { getDefaultCategoryId } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'

import { makeDateTime } from '@src/lib/util'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import Yup, { notTooOld } from '@src/lib/yup'

import { SeaFile } from '@src/lib/fileImports'
import { VesselDocument } from '@src/shared-state/VesselDocuments/vesselDocuments'
import { IntervalDropdown } from '@src/components/_molecules/IntervalDropdown/IntervalDropdown'
import { Text, View } from 'react-native'
import {
  UpdateVesselDocumentDto,
  UpdateVesselDocumentUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/UpdateVesselDocumentUseCase'
import {
  CreateVesselDocumentDto,
  CreateVesselDocumentUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/CreateVesselDocumentUseCase'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { HALF_WIDTH_DESKTOP, DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

enum DocumentationType {
  Controlled = 'sfdoc',
  ExternalFiles = 'files',
}

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  type: Yup.string().max(500).required(),
  dateExpires: Yup.date().when('type', {
    is: 'renewable',
    then: schema => schema.required().min(...notTooOld),
  }),
  interval: Yup.string().when('type', {
    is: 'renewable',
    then: schema => schema.max(4).required(),
  }),
  emailReminder: Yup.string().when('type', {
    is: 'renewable',
    then: schema => schema.max(200),
  }),
  categoryId: Yup.string().max(500).required(),
})

export interface EditVesselDocumentDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: VesselDocument
  mode?: DrawerMode
}
export function EditVesselDocumentDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditVesselDocumentDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselDocumentCategories = sharedState.vesselDocumentCategories.use(visible)

  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const documentTypeOptions = useMemo(() => {
    return [
      {
        label: `Renewable ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'}`,
        value: 'renewable',
      },
      {
        label: `Non-Expiring ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'}`,
        value: 'nonExpiring',
      },
    ]
  }, [vessel])

  const categoriesOptions = useMemo(() => {
    if (!vesselDocumentCategories) return []

    const categories = vesselDocumentCategories?.ids.map(id => {
      const category = vesselDocumentCategories.byId[id]
      return {
        label: category.name,
        value: category.id,
      }
    })

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...(categories ?? []),
    ]
  }, [vesselDocumentCategories])

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      type: selectedItem?.type ?? 'renewable',
      dateExpires: selectedItem?.dateExpires ?? '',
      emailReminder: selectedItem?.emailReminder ?? '',
      categoryId: selectedItem?.categoryId ?? getDefaultCategoryId('General', vesselDocumentCategories),
      interval: selectedItem?.interval ?? '',
      documentationType:
        selectedItem?.files && selectedItem.files.length > 0
          ? DocumentationType.ExternalFiles
          : DocumentationType.Controlled,
      files: selectedItem?.files ?? [],
      sfdoc: selectedItem?.sfdoc ?? {},
    }
  }, [selectedItem, vesselDocumentCategories])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (values.type === 'renewable' && values.dateExpires && values.emailReminder) {
        dateToRemind = subtractInterval(values.dateExpires, values.emailReminder).toISODate() ?? undefined
      }

      const commonDto = {
        vesselId,
        title: values.title,
        dateExpires:
          values.type === 'renewable' && values.dateExpires
            ? (formatSeaDate(values.dateExpires) ?? undefined)
            : undefined,
        emailReminder: values.type === 'renewable' && values.emailReminder ? values.emailReminder : undefined,
        interval: values.type === 'renewable' && values.interval ? values.interval : undefined,
        categoryId: values.categoryId,
        dateToRemind,
        files: files,
        type: values.type,
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateVesselDocumentDto = {
          ...commonDto,
        }
        const createVesselDocument = services.get(CreateVesselDocumentUseCase)
        createVesselDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Vessel Document\n ${err.message}`))
      } else {
        const dto: UpdateVesselDocumentDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateVesselDocument = services.get(UpdateVesselDocumentUseCase)

        updateVesselDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Vessel Document\n ${err.message}`))
      }
    },
    [vessel, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Vessel Document' : 'Edit Vessel Document'}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Vessel Document'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'Document Title'}
            showIcon={true}
            value={formik.values.title}
            onChangeText={formik.handleChange('title')}
            errorText={formik.errors.title}
            hasError={!!formik.errors.title}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaDropdown
            label={'Document Type'}
            showIcon={true}
            items={documentTypeOptions}
            style={{ flex: 1, width: '100%' }}
            onSelect={value => formik.setFieldValue('type', value)}
            value={formik.values.type}
          />

          <SeaDropdown
            label={'Category'}
            showIcon={true}
            items={categoriesOptions}
            style={{ flex: 1, width: '100%' }}
            onSelect={value => formik.setFieldValue('categoryId', value)}
            value={formik.values.categoryId}
          />
        </DrawerRow>

        {formik.values.type.includes('renewable') && (
          <>
            <DrawerRow>
              <DrawerColumn>
                <SeaDateTimeInput
                  value={makeDateTime(formik.values.dateExpires)}
                  onChange={date => formik.setFieldValue('dateExpires', date)}
                  type={'date'}
                  label="Expiry Date"
                  showIcon={true}
                  style={{ flex: 1, width: '100%' }}
                  errorText={formik.errors.dateExpires}
                  hasError={!!formik.errors.dateExpires}
                />
              </DrawerColumn>

              <DrawerColumn>
                <IntervalDropdown
                  onChange={value => formik.setFieldValue('interval', value)}
                  value={formik.values.interval}
                  label="Renewal Interval"
                  showIcon={true}
                  errorText={formik.errors.interval}
                  hasError={!!formik.errors.interval}
                />
              </DrawerColumn>
            </DrawerRow>
            <DrawerRow width={HALF_WIDTH_DESKTOP}>
              <SeaEmailReminderDropdown
                label="Email Reminder"
                showIcon={true}
                value={formik.values.emailReminder}
                onSelect={value => formik.setFieldValue('emailReminder', value)}
                style={{ flex: 1, width: '100%' }}
              />
            </DrawerRow>
          </>
        )}
        <SeaDropdown
          label={'Documentation'}
          showIcon={true}
          items={[
            {
              label: 'Controlled',
              value: DocumentationType.Controlled,
            },
            {
              label: 'External File(s)',
              value: DocumentationType.ExternalFiles,
            },
          ]}
          style={{
            width: '100%',
          }}
          onSelect={value => formik.setFieldValue('documentationType', value)}
          value={formik.values.documentationType}
        />
        {formik.values.documentationType === DocumentationType.ExternalFiles ? (
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        ) : (
          <Text>TODO Rich text editor</Text>
        )}
      </DrawerContent>

      {/* TODO: Add Links */}
    </SeaDrawer>
  )
}

import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'

import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'

import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { VesselCertificate } from '@src/shared-state/VesselDocuments/vesselCertificates'
import { FormikValues, useFormik } from 'formik'
import { renderFullName } from '@src/shared-state/Core/users'
import { formatSeaDate, subtractInterval } from '@src/lib/datesAndTime'
import { getDefaultCategoryId } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'

import { makeDateTime } from '@src/lib/util'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import {
  UpdateVesselCertificateDto,
  UpdateVesselCertificateUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/UpdateVesselCertificateUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import {
  CreateVesselCertificateDto,
  CreateVesselCertificateUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/CreateVesselCertificateUseCase'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import Yup, { notTooOld } from '@src/lib/yup'

import { SeaFile } from '@src/lib/fileImports'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  dateExpires: Yup.date().when('type', {
    is: 'renewable',
    then: schema => schema.required().min(...notTooOld),
  }),
})

export interface EditVesselCertificateDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: VesselCertificate
  mode?: DrawerMode
}
export function EditVesselCertificateDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditVesselCertificateDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselCertificateCategories = sharedState.vesselCertificateCategories.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const certTypeOptions = useMemo(() => {
    return [
      {
        label: `Renewable ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'}`,
        value: 'renewable',
      },
      {
        label: `Non-Expiring ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'}`,
        value: 'nonExpiring',
      },
    ]
  }, [vessel])

  const categoriesOptions = useMemo(() => {
    if (!vesselCertificateCategories) return []

    const categories = vesselCertificateCategories?.ids.map(id => {
      const category = vesselCertificateCategories.byId[id]
      return {
        label: category.name,
        value: category.id,
      }
    })

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...(categories ?? []),
    ]
  }, [vesselCertificateCategories])

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      certNum: selectedItem?.certNum ?? '',
      issuedBy: selectedItem?.issuedBy ?? renderFullName(),
      dateIssued: selectedItem?.dateIssued ?? formatSeaDate(),
      type: selectedItem?.type ?? 'renewable',
      dateExpires: selectedItem?.dateExpires ?? '',
      emailReminder: selectedItem?.emailReminder ?? '',
      categoryId: selectedItem?.categoryId ?? getDefaultCategoryId('General', vesselCertificateCategories),
    }
  }, [selectedItem, vesselCertificateCategories])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (values.type === 'renewable' && values.dateExpires && values.emailReminder) {
        dateToRemind = subtractInterval(values.dateExpires, values.emailReminder).toISODate() ?? undefined
      }

      const commonDto = {
        vesselId,
        title: values.title,
        certNum: values.certNum ?? undefined,
        issuedBy: values.issuedBy ?? undefined,
        dateIssued: formatSeaDate(values.dateIssued) ?? '',
        dateExpires: formatSeaDate(values.dateExpires) ?? undefined,
        emailReminder: values.emailReminder ?? undefined,
        categoryId: values.categoryId,
        dateToRemind,
        files: files,
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateVesselCertificateDto = {
          ...commonDto,
          isShoreFacility: vessel.isShoreFacility ?? false,
          type: values.type,
        }

        const createVesselCertificate = services.get(CreateVesselCertificateUseCase)

        createVesselCertificate
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Vessel Certificate\n ${err.message}`))
      } else {
        const dto: UpdateVesselCertificateDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateVesselCertificate = services.get(UpdateVesselCertificateUseCase)

        updateVesselCertificate
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Vessel Certificate\n ${err.message}`))
      }
    },
    [vessel, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  return (
    <SeaDrawer
      title={`${mode === DrawerMode.Create ? 'Add' : 'Edit'} ${vessel?.isShoreFacility ? 'Certification' : 'Certificate'}`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Certificate'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <DrawerColumn>
            <SeaTextInput
              label={(vessel?.isShoreFacility ? 'Certification' : 'Certificate') + ' Title'}
              showIcon={true}
              value={formik.values.title}
              onChangeText={formik.handleChange('title')}
              hasError={Boolean(errors.title && touched.title)}
              errorText={errors.title}
            />
          </DrawerColumn>

          <DrawerColumn>
            <SeaTextInput
              label={`${vessel?.isShoreFacility ? 'Certification' : 'Certificate'} #`}
              showIcon={true}
              labelIconOptions={{ icon: 'license' }}
              value={formik.values.certNum}
              onChangeText={formik.handleChange('certNum')}
            />
          </DrawerColumn>
        </DrawerRow>

        <DrawerRow>
          <DrawerColumn>
            <SeaTextInput
              label="Issued By"
              showIcon={true}
              labelIconOptions={{ icon: 'fact_check' }}
              value={formik.values.issuedBy}
              onChangeText={formik.handleChange('issuedBy')}
            />
          </DrawerColumn>

          <SeaDateTimeInput
            value={makeDateTime(formik.values.dateIssued)}
            onChange={date => formik.setFieldValue('dateIssued', date)}
            type={'date'}
            label="Issue Date"
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
          />
        </DrawerRow>

        <DrawerRow>
          <DrawerColumn>
            <SeaDropdown
              label={(vessel?.isShoreFacility ? 'Certification' : 'Certificate') + ' Type'}
              showIcon={true}
              items={certTypeOptions}
              style={{
                width: '100%',
              }}
              onSelect={value => formik.setFieldValue('type', value)}
              value={formik.values.type}
              disabled={mode === DrawerMode.Edit}
            />
          </DrawerColumn>

          <DrawerColumn>
            <SeaDropdown
              label={'Category'}
              showIcon={true}
              items={categoriesOptions}
              style={{
                width: '100%',
              }}
              onSelect={value => formik.setFieldValue('categoryId', value)}
              value={formik.values.categoryId}
              disabled={!vesselCertificateCategories}
            />
          </DrawerColumn>
        </DrawerRow>

        {formik.values.type.includes('renewable') && (
          <DrawerRow>
            <SeaDateTimeInput
              value={makeDateTime(formik.values.dateExpires)}
              onChange={date => formik.setFieldValue('dateExpires', date)}
              type={'date'}
              label="Expiry Date"
              showIcon={true}
              style={{ flex: 1, width: '100%' }}
              hasError={Boolean(errors.dateExpires && touched.dateExpires)}
              errorText={errors.dateExpires}
            />

            <SeaEmailReminderDropdown
              label="Email Reminder"
              showIcon={true}
              value={formik.values.emailReminder}
              onSelect={value => formik.setFieldValue('emailReminder', value)}
              style={{ flex: 1, width: '100%' }}
            />
          </DrawerRow>
        )}
        <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
      </DrawerContent>
    </SeaDrawer>
  )
}

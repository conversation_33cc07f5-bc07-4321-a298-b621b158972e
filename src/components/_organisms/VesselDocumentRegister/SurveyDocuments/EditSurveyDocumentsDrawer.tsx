import React, { useCallback, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SurveyReport } from '@src/shared-state/VesselDocuments/vesselSurveyReports'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { FormikValues, useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaFile } from '@src/lib/fileImports'
import Yup, { notTooOld } from '@src/lib/yup'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { DateTime } from 'luxon'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { formatSeaDate, makeDateTime } from '@src/lib/datesAndTime'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import {
  UpdateSurveyDocumentDto,
  UpdateSurveyDocumentUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/UpdateSurveyDocumentUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  CreateSurveyDocumentDto,
  CreateSurveyDocumentUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/CreateSurveyDocumentUseCase'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent, HALF_WIDTH_DESKTOP } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'
import { DrawerColumn } from '@src/components/_molecules/SeaDrawer/DrawerColumn'

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  dateSurveyed: Yup.date()
    .required()
    .min(...notTooOld),
  surveyor: Yup.string().max(500),
  personnelPresent: Yup.string().max(500),
  location: Yup.string().max(500),
  inOrOutWater: Yup.string().max(3),
})

export interface EditSurveyDocumentsDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: SurveyReport
  mode?: DrawerMode
}

export function EditSurveyDocumentsDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditSurveyDocumentsDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  const [files, setFiles] = useState<SeaFile[]>([])

  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      dateSurveyed: selectedItem?.dateSurveyed ?? DateTime.now().toISO(),
      surveyor: selectedItem?.surveyor ?? '',
      personnelPresent: selectedItem?.personnelPresent ?? '',
      location: selectedItem?.location ?? '',
      inOrOutWater: selectedItem?.inOrOutWater ?? 'N/A',
    }
  }, [selectedItem])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      const commonDto = {
        vesselId,
        title: values.title,
        dateSurveyed: formatSeaDate(values.dateSurveyed) ?? undefined,
        surveyor: values.surveyor ?? undefined,
        personnelPresent: values.personnelPresent ?? undefined,
        location: values.location ?? undefined,
        inOrOutWater: values.inOrOutWater ?? undefined,
        files: files,
      }

      if (mode === DrawerMode.Create) {
        const dto: CreateSurveyDocumentDto = {
          ...commonDto,
        }
        const createSurveyDocument = services.get(CreateSurveyDocumentUseCase)
        createSurveyDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Survey Document\n ${err.message}`))
      } else {
        const dto: UpdateSurveyDocumentDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateSurveyDocument = services.get(UpdateSurveyDocumentUseCase)

        updateSurveyDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Survey Document\n ${err.message}`))
      }
    },
    [vessel, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })
  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Survey Report' : 'Edit Survey Report'}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Survey Report'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'Document Title'}
            showIcon={true}
            value={formik.values.title}
            onChangeText={formik.handleChange('title')}
            errorText={formik.errors.title ?? undefined}
            hasError={!!formik.errors.title}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaDateTimeInput
            value={makeDateTime(formik.values.dateSurveyed)}
            onChange={date => formik.setFieldValue('dateSurveyed', date)}
            type={'date'}
            label="Survey Date"
            showIcon={true}
            style={{ flex: 1, width: '100%' }}
            errorText={formik.errors.dateSurveyed ?? undefined}
            hasError={!!formik.errors.dateSurveyed}
          />
          <DrawerColumn>
            <SeaTextInput
              label="Surveyor"
              showIcon={true}
              labelIconOptions={{ icon: 'person' }}
              value={formik.values.surveyor}
              onChangeText={formik.handleChange('surveyor')}
              errorText={formik.errors.surveyor ?? undefined}
              hasError={!!formik.errors.surveyor}
            />
          </DrawerColumn>
        </DrawerRow>

        <DrawerRow>
          <DrawerColumn>
            <SeaTextInput
              label="Personnel Present"
              showIcon={true}
              labelIconOptions={{ icon: 'person' }}
              value={formik.values.personnelPresent}
              onChangeText={formik.handleChange('personnelPresent')}
              errorText={formik.errors.personnelPresent ?? undefined}
              hasError={!!formik.errors.personnelPresent}
            />
          </DrawerColumn>
          <DrawerColumn>
            <SeaTextInput
              label="Location"
              showIcon={true}
              labelIconOptions={{ icon: 'location_on' }}
              value={formik.values.location}
              onChangeText={formik.handleChange('location')}
              errorText={formik.errors.location ?? undefined}
              hasError={!!formik.errors.location}
            />
          </DrawerColumn>
        </DrawerRow>

        <DrawerRow width={HALF_WIDTH_DESKTOP}>
          <SeaDropdown
            label="In or Out of water"
            showIcon={true}
            items={[
              {
                label: 'N/A',
                value: 'N/A',
              },
              {
                label: 'In',
                value: 'In',
              },
              {
                label: 'Out',
                value: 'Out',
              },
            ]}
            style={{
              width: '100%',
            }}
            onSelect={value => formik.setFieldValue('inOrOutWater', value)}
            value={formik.values.inOrOutWater}
          />
        </DrawerRow>

        <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
      </DrawerContent>
    </SeaDrawer>
  )
}

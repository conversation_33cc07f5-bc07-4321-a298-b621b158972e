import React, { useMemo, useState } from 'react'
import { KeyboardAvoidingView, StyleSheet, ViewStyle } from 'react-native'
import { DrawerMode, SeaDrawer } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { sharedState } from '@src/shared-state/shared-state'
import { IntervalDropdown } from '@src/components/_molecules/IntervalDropdown/IntervalDropdown'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { useFormik } from 'formik'
import Yup from '@src/lib/yup'
import { CrewSelectInput } from '@src/components/_molecules/CrewSelectInput/CrewSelectInput'
import { UserType } from '@src/shared-state/Core/user'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { UpdateDrillDto, UpdateDrillUseCase } from '@src/domain/use-cases/safety/UpdateDrillUseCase'
import { CreateDrillDto, CreateDrillUseCase } from '@src/domain/use-cases/safety/CreateDrillUseCase'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

interface ModifyDrillDrawerProps {
  visible: boolean
  onClose: () => void
  mode: DrawerMode
  selectedDrill?: Drill
  style?: ViewStyle
}

const validationSchema = Yup.object({
  name: Yup.string().required('Drill name is required').min(1, 'Drill name cannot be empty'),
  interval: Yup.string().required('Interval is required'),
  notAssignedTo: Yup.array().of(Yup.string()),
})

export const useInitialDrillValues = (drill?: Drill) => {
  return useMemo(() => {
    return {
      name: drill?.name ?? '',
      interval: drill?.interval ?? '',
      notAssignedTo: drill?.notAssignedTo ?? [],
    }
  }, [drill])
}

export const ModifyDrillDrawer: React.FC<ModifyDrillDrawerProps> = ({
  visible,
  onClose,
  mode,
  selectedDrill,
  style,
}) => {
  const title = useMemo(() => (mode === DrawerMode.Edit ? 'Update Drill' : 'Add Drill'), [mode])

  const logger = useLogger(`ModifyDrillDrawer:${mode}`)

  const userId = sharedState.userId.use()
  const users = sharedState.users.use()
  const vesselId = sharedState.vesselId.use()
  const licenseeId = sharedState.licenseeId.use()

  const initialValues = useInitialDrillValues(selectedDrill)

  const crewSelectionData = useMemo(() => {
    if (!vesselId) return []

    const vesselUsers = users?.byVesselId[vesselId]

    if (!vesselUsers) return []

    return vesselUsers
      .filter((u: UserType) => u.state === 'active')
      .map(u => ({ label: renderFullNameForUserId(u.id), value: u.id }))
  }, [users, vesselId])

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  const serviceContainer = useServiceContainer()
  const updateDrillUseCase = serviceContainer.get(UpdateDrillUseCase)
  const createDrillUseCase = serviceContainer.get(CreateDrillUseCase)

  const executeUpdate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    if (!selectedDrill?.id) {
      throw new Error('Cannot update drill without ID')
    }

    const dto: UpdateDrillDto = {
      drillId: selectedDrill.id,
      vesselId,
      name: values.name,
      interval: values.interval,
      notAssignedTo: values.notAssignedTo,
      currentDrill: selectedDrill,
    }

    updateDrillUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Drill updated successfully')
      })
      .catch(err => logger.error(`Error updating drill\n ${err.message}`, err))
  }

  const executeCreate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const dto: CreateDrillDto = {
      vesselId,
      name: values.name,
      interval: values.interval,
      notAssignedTo: values.notAssignedTo,
    }

    createDrillUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Drill created successfully')
      })
      .catch(err => logger.error(`Error creating drill\n ${err.message}`, err))
  }

  const doSubmit = (values: typeof initialValues) => {
    if (!userId || !licenseeId || !vesselId) {
      throw new Error('Missing Licensee or User')
    }

    if (mode === DrawerMode.Edit) {
      executeUpdate(values, vesselId, userId, licenseeId)
    } else if (mode === DrawerMode.Create) {
      executeCreate(values, vesselId, userId, licenseeId)
    }

    resetForm()
    onClose()
  }

  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Drill'}
          edit={{ onSubmit: handleSubmit }}
          create={{ onSubmit: handleSubmit }}
        />
      }>
      <KeyboardAvoidingView>
        <DrawerContent>
          {/* Row 1 - Drill Name */}
          <DrawerRow>
            <SeaTextInput
              label={'Drill Name'}
              showIcon={true}
              value={values.name}
              onChangeText={handleChange('name')}
              hasError={Boolean(errors.name && touched.name)}
              errorText={errors.name}
              style={{ flex: 1, width: '100%' }}
            />
          </DrawerRow>

          {/* Row 2 - Interval */}
          <DrawerRow>
            <IntervalDropdown
              showIcon={true}
              value={values.interval}
              onChange={handleChange('interval')}
              hasError={Boolean(errors.interval && touched.interval)}
              errorText={errors.interval}
              style={{ width: '100%', flex: 1 }}
            />
          </DrawerRow>

          {/* Row 3 - Excluded Users */}
          <DrawerRow>
            <CrewSelectInput
              label={'Excluded Users'}
              showIcon={true}
              selectedIds={values.notAssignedTo}
              data={crewSelectionData}
              onChange={excludedUsers => setFieldValue('notAssignedTo', excludedUsers)}
            />
          </DrawerRow>

          <SeaSpacer height={50} />
        </DrawerContent>
      </KeyboardAvoidingView>
    </SeaDrawer>
  )
}
